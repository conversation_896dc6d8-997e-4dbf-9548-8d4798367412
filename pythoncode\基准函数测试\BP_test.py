"""
BP评估函数的收益递减特性和单调性测试
专门测试BP算法的两个重要特性：
1. 单调性：添加更多种子节点时，影响力不会减少
2. 收益递减：每增加一个种子节点的边际收益递减
"""
import networkx as nx
import numpy as np
import matplotlib.pyplot as plt
import time

def BP_influence(S, G, p, max_hop=5):
    """
    BP近似影响力估计（全节点递推）
    Args:
        S: 种子节点集合
        G: NetworkX图对象
        p: 传播概率
        max_hop: 最大递推轮数
    Returns:
        float: 估计影响力值
    """
    S = set(S)
    nodes = list(G.nodes())
    # 初始化激活概率
    P = {v: 1.0 if v in S else 0.0 for v in nodes}

    for _ in range(max_hop):
        new_P = P.copy()
        for v in nodes:
            if v in S:
                continue
            parents = list(G.neighbors(v))
            prob_not = 1.0
            for u in parents:
                prob_not *= 1 - p * P[u]
            new_P[v] = 1 - prob_not
        P = new_P
    return sum(P.values())

def create_test_graph():
    """创建一个简单的测试图用于验证BP函数特性"""
    G = nx.Graph()
    # 创建一个星形图：中心节点连接到多个叶子节点
    G.add_edges_from([(0, i) for i in range(1, 6)])  # 节点0连接到1,2,3,4,5
    # 添加一些额外的边形成更复杂的结构
    G.add_edges_from([(1, 6), (2, 7), (3, 8), (6, 7), (7, 8)])
    return G

def test_monotonicity(G, p=0.1, max_seeds=5):
    """
    测试BP函数的单调性
    Args:
        G: 网络图
        p: 传播概率
        max_seeds: 最大种子节点数
    Returns:
        tuple: (种子集合列表, 影响力值列表, 是否单调)
    """
    print(f"\n=== 测试BP函数单调性 (p={p}) ===")
    
    # 按度中心性排序选择种子节点
    degree_centrality = nx.degree_centrality(G)
    sorted_nodes = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)
    
    seed_sets = []
    influences = []
    
    for k in range(1, min(max_seeds + 1, len(G.nodes()))):
        seeds = [node for node, _ in sorted_nodes[:k]]
        influence = BP_influence(seeds, G, p)
        
        seed_sets.append(seeds.copy())
        influences.append(influence)
        
        print(f"种子集合 {seeds}: 影响力 = {influence:.4f}")
    
    # 检查单调性
    is_monotonic = all(influences[i] <= influences[i+1] for i in range(len(influences)-1))
    
    print(f"单调性检查: {'通过' if is_monotonic else '失败'}")
    if not is_monotonic:
        for i in range(len(influences)-1):
            if influences[i] > influences[i+1]:
                print(f"  违反单调性: {influences[i]:.4f} > {influences[i+1]:.4f}")
    
    return seed_sets, influences, is_monotonic

def test_diminishing_returns(G, p=0.1, max_seeds=8):
    """
    测试BP函数的收益递减特性
    Args:
        G: 网络图
        p: 传播概率
        max_seeds: 最大种子节点数
    Returns:
        tuple: (种子集合列表, 影响力值列表, 边际收益列表, 是否收益递减)
    """
    print(f"\n=== 测试BP函数收益递减特性 (p={p}) ===")
    
    # 按度中心性排序选择种子节点
    degree_centrality = nx.degree_centrality(G)
    sorted_nodes = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)
    
    seed_sets = []
    influences = []
    marginal_gains = []
    
    for k in range(1, min(max_seeds + 1, len(G.nodes()))):
        seeds = [node for node, _ in sorted_nodes[:k]]
        influence = BP_influence(seeds, G, p)
        
        seed_sets.append(seeds.copy())
        influences.append(influence)
        
        if k == 1:
            marginal_gain = influence  # 第一个种子的边际收益就是总影响力
        else:
            marginal_gain = influence - influences[-2]  # 当前影响力 - 前一个影响力
        
        marginal_gains.append(marginal_gain)
        
        print(f"种子集合 {seeds}: 影响力 = {influence:.4f}, 边际收益 = {marginal_gain:.4f}")
    
    # 检查收益递减
    is_diminishing = all(marginal_gains[i] >= marginal_gains[i+1] for i in range(len(marginal_gains)-1))
    
    print(f"收益递减检查: {'通过' if is_diminishing else '失败'}")
    if not is_diminishing:
        for i in range(len(marginal_gains)-1):
            if marginal_gains[i] < marginal_gains[i+1]:
                print(f"  违反收益递减: {marginal_gains[i]:.4f} < {marginal_gains[i+1]:.4f}")
    
    return seed_sets, influences, marginal_gains, is_diminishing

def plot_results(seed_sets, influences, marginal_gains, title_prefix="BP函数"):
    """绘制测试结果"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 绘制影响力曲线
    k_values = range(1, len(influences) + 1)
    ax1.plot(k_values, influences, 'bo-', linewidth=2, markersize=8)
    ax1.set_xlabel('种子节点数量')
    ax1.set_ylabel('影响力值')
    ax1.set_title(f'{title_prefix} - 影响力随种子数量变化')
    ax1.grid(True, alpha=0.3)
    
    # 绘制边际收益曲线
    ax2.plot(k_values, marginal_gains, 'ro-', linewidth=2, markersize=8)
    ax2.set_xlabel('种子节点数量')
    ax2.set_ylabel('边际收益')
    ax2.set_title(f'{title_prefix} - 边际收益变化')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def test_different_probabilities(G, probabilities=[0.01, 0.05, 0.1, 0.2]):
    """测试不同传播概率下的BP函数特性"""
    print("\n" + "="*60)
    print("测试不同传播概率下的BP函数特性")
    print("="*60)
    
    all_results = {}
    
    for p in probabilities:
        print(f"\n传播概率 p = {p}")
        print("-" * 30)
        
        # 测试单调性
        seed_sets, influences, is_monotonic = test_monotonicity(G, p, max_seeds=6)
        
        # 测试收益递减
        _, _, marginal_gains, is_diminishing = test_diminishing_returns(G, p, max_seeds=6)
        
        all_results[p] = {
            'influences': influences,
            'marginal_gains': marginal_gains,
            'is_monotonic': is_monotonic,
            'is_diminishing': is_diminishing
        }
        
        print(f"结果总结: 单调性={'通过' if is_monotonic else '失败'}, "
              f"收益递减={'通过' if is_diminishing else '失败'}")
    
    return all_results

def main():
    """主测试函数"""
    print("BP评估函数特性测试")
    print("="*50)
    
    # 创建测试图
    G = create_test_graph()
    print(f"测试图信息: {G.number_of_nodes()} 个节点, {G.number_of_edges()} 条边")
    
    # 测试不同传播概率
    results = test_different_probabilities(G)
    
    # 可视化结果（选择一个概率进行展示）
    p_demo = 0.1
    if p_demo in results:
        plot_results(
            None,  # seed_sets不需要用于绘图
            results[p_demo]['influences'],
            results[p_demo]['marginal_gains'],
            f"BP函数 (p={p_demo})"
        )
    
    print("\n" + "="*50)
    print("测试完成！")

if __name__ == "__main__":
    main()
